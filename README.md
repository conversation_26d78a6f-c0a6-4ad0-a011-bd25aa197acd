# BMS Pulse

BMS Pulse is a SaaS platform for development analytics that integrates with GitHub to provide insights about team performance, developer metrics, and project health.

## Features

- GitHub integration via GitHub App and webhooks
- Developer performance metrics
- Repository health analytics
- Team collaboration insights
- Customizable dashboards
- Automated reporting
- Alert system for metric thresholds
- Authentication with Firebase
- Internationalization support

## Technology Stack

- **Framework**: [Next.js](https://nextjs.org/) with App Router
- **UI Library**: [Material UI](https://mui.com/)
- **Styling**: [Tailwind CSS](https://tailwindcss.com/)
- **Authentication**: [Firebase Authentication](https://firebase.google.com/docs/auth)
- **Database**: PostgreSQL with TimescaleDB extension
- **ORM**: Prisma
- **Caching**: Redis
- **Charts**: [ApexCharts](https://apexcharts.com/)
- **Forms**: [React Hook Form](https://react-hook-form.com/)
- **TypeScript**: For type safety and better developer experience

## Project Structure

### Repository Structure

```
bms-tech-pulse/
├── application/        # Next.js application with API routes
├── database/           # Database migrations and TimescaleDB configurations
├── docs/               # Documentation
├── nginx/              # Nginx configuration for production
└── docker-compose.yml  # Docker Compose configuration
```

### Application Structure

```
application/
├── public/                # Static assets
├── src/
│   ├── app/               # Next.js App Router pages and API routes
│   │   ├── api/           # API routes
│   │   ├── auth/          # Authentication pages
│   │   ├── dashboard/     # Dashboard and feature pages
│   │   ├── layout.tsx     # Root layout
│   │   └── page.tsx       # Home page
│   ├── components/        # Reusable UI components
│   ├── contexts/          # React contexts for state management
│   ├── hooks/             # Custom React hooks
│   ├── i18n/              # Internationalization configuration
│   ├── lib/               # Utility functions and helpers
│   ├── services/          # Service layer for API interactions
│   ├── styles/            # Global styles and Tailwind configuration
│   └── types/             # TypeScript type definitions
├── messages/              # Translation files
├── .env.example           # Example environment variables
├── next.config.ts         # Next.js configuration
├── package.json           # Dependencies and scripts
└── tsconfig.json          # TypeScript configuration
```

## Getting Started

### Prerequisites

- Docker and Docker Compose (for full-stack development)
- Node.js (v16+)
- npm or yarn
- Firebase project (for authentication)

### Setup Options

#### Option 1: Docker Compose (Full Stack)

1. Clone the repository:

   ```bash
   git clone https://github.com/build-manage-scale/pulse.git
   cd pulse
   ```

2. Create environment files:

   ```bash
   cp application/.env.example application/.env
   ```

3. Update the `.env` file with your Firebase configuration and other settings.

4. Start all services using Docker Compose:

   ```bash
   docker-compose up -d
   ```

5. Access the services:
   - Application: http://localhost:3000

#### Option 2: Local Development (Application Only)

1. Clone the repository:

   ```bash
   git clone https://github.com/build-manage-scale/pulse.git
   cd pulse/application
   ```

2. Install dependencies:

   ```bash
   npm install
   # or
   yarn
   ```

3. Create a `.env` file based on the example:

   ```bash
   cp .env.example .env
   ```

4. Update the `.env` file with your Firebase configuration and other settings.

5. Start the development server:

   ```bash
   npm run dev
   # or
   yarn dev
   ```

6. Open [http://localhost:3000](http://localhost:3000) in your browser.

## Development

### Code Style

The project uses ESLint and Prettier for code formatting and style checking. Run the linter with:

```bash
npm run lint
# or
yarn lint
```

### Database Migrations

The project uses Prisma ORM for database access. To run migrations:

```bash
npx prisma migrate dev --name your-migration-name
```

### Building for Production

To build the application for production:

```bash
npm run build
# or
yarn build
```

The build output will be in the `.next` directory.

### Testing

Run tests with:

```bash
npm test
# or
yarn test
```

## Authentication

The application uses Firebase Authentication for user management. The authentication flow is implemented in the `src/lib/auth.ts` file and used throughout the application.

## Documentation

For more detailed documentation, see the [docs](../docs) directory:

- [Project Summary](../docs/summary.md)
- [Getting Started Guide](../docs/getting-started.md)
- [GitHub App Setup](../docs/github-app-setup.md)
- [Firebase Deployment](../docs/firebase-deployment.md)
- [Development Backlog](../docs/backlog.md)

## License

[MIT](../LICENSE)
