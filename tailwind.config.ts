import type { Config } from 'tailwindcss';

const config: Config = {
  content: [
    './src/pages/**/*.{js,ts,jsx,tsx,mdx}',
    './src/components/**/*.{js,ts,jsx,tsx,mdx}',
    './src/app/**/*.{js,ts,jsx,tsx,mdx}',
  ],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Core Colors (Midnight + Electric-Blue palette)
        'bg-primary': '#0D1117', // nearly-black midnight
        'bg-surface': '#161B22', // elevated cards & sidebars
        'border-subtle': '#30363D',
        'text-primary': '#F8FAFC', // 98% white
        'text-muted': '#94A3B8',
        
        // Accent Colors
        'accent-primary': '#3B82F6', // electric blue
        'accent-hover': '#2563EB',
        'accent-subtle': '#1E40AF',
        
        // Semantic Colors
        success: '#22C55E',
        warning: '#FACC15',
        danger: '#EF4444',
        info: '#0EA5E9',
        
        // Extended palette for more granular control
        midnight: {
          50: '#f8fafc',
          100: '#f1f5f9',
          200: '#e2e8f0',
          300: '#cbd5e1',
          400: '#94a3b8',
          500: '#64748b',
          600: '#475569',
          700: '#334155',
          800: '#1e293b',
          900: '#161B22',
          950: '#0D1117',
        },
        electric: {
          50: '#eff6ff',
          100: '#dbeafe',
          200: '#bfdbfe',
          300: '#93c5fd',
          400: '#60a5fa',
          500: '#3b82f6',
          600: '#2563eb',
          700: '#1d4ed8',
          800: '#1e40af',
          900: '#1e3a8a',
          950: '#172554',
        },
      },
      borderRadius: {
        'card': '12px',
        'button': '12px',
      },
      spacing: {
        // 8pt spacing grid
        '1': '0.125rem', // 2px
        '2': '0.25rem',  // 4px
        '3': '0.375rem', // 6px
        '4': '0.5rem',   // 8px
        '6': '0.75rem',  // 12px
        '8': '1rem',     // 16px
        '12': '1.5rem',  // 24px
        '16': '2rem',    // 32px
        '20': '2.5rem',  // 40px
        '24': '3rem',    // 48px
        '32': '4rem',    // 64px
      },
      boxShadow: {
        'card': '0 2px 6px rgba(0, 0, 0, 0.4)',
        'card-hover': '0 4px 12px rgba(0, 0, 0, 0.5)',
        'modal': '0 8px 32px rgba(0, 0, 0, 0.6)',
      },
      fontFamily: {
        sans: [
          'Inter',
          '-apple-system',
          'BlinkMacSystemFont',
          'Segoe UI',
          'Helvetica',
          'Arial',
          'sans-serif',
          'Apple Color Emoji',
          'Segoe UI Emoji',
        ],
        mono: ['Roboto Mono', 'monospace'],
      },
      fontSize: {
        'xs': ['0.75rem', { lineHeight: '1.66' }],
        'sm': ['0.875rem', { lineHeight: '1.57' }],
        'base': ['1rem', { lineHeight: '1.5' }],
        'lg': ['1.125rem', { lineHeight: '1.44' }],
        'xl': ['1.25rem', { lineHeight: '1.4' }],
        '2xl': ['1.5rem', { lineHeight: '1.33' }],
        '3xl': ['1.875rem', { lineHeight: '1.27' }],
        '4xl': ['2.25rem', { lineHeight: '1.22' }],
      },
      animation: {
        'fade-in': 'fadeIn 0.2s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
        'scale-in': 'scaleIn 0.2s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
        scaleIn: {
          '0%': { transform: 'scale(0.95)', opacity: '0' },
          '100%': { transform: 'scale(1)', opacity: '1' },
        },
      },
    },
  },
  plugins: [],
};

export default config;
