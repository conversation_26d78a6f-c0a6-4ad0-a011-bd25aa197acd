# BMS Pulse - Technical Architecture

This document provides a detailed overview of the BMS Pulse system architecture and technical components.

## System Architecture

BMS Pulse follows a modern architecture with the following components:

```
                    ┌─────────────┐
                    │   GitHub    │
                    │     API     │
                    └─────────────┘
                          ▲
                          │
                          │
                    ┌─────────────┐      ┌─────────────┐
                    │ Application │────▶│  Database   │
                    │  (Next.js)  │      │ (PostgreSQL)│
                    └─────────────┘      └─────────────┘
                       ▲  │  ▲
                       │  │  │
                       │  │  │
                       │  │  └─────────┐
                       │  │            │
                       ▼  ▼            │
            ┌─────────────┐     ┌─────────────┐
            │   Firebase  │     │    Redis    │
            │    Auth     │     │    Cache    │
            └─────────────┘     └─────────────┘
```

## Key Components

### Application Architecture

- **Framework**: Next.js with App Router
- **API Layer**: Next.js API Routes with TypeScript
- **UI Components**: Material UI and Tailwind CSS
- **State Management**: React Context API and custom hooks
- **Data Access Layer**: Prisma ORM for type-safe database access
- **Authentication**:
  - Client-side: Firebase Authentication
  - Server-side: Firebase Admin SDK for token verification
- **GitHub Integration**:
  - GitHub App for repository access
  - Webhook handlers for real-time events
  - Octokit SDK for GitHub API interactions
- **Caching**: Redis for performance optimization and rate limit management
- **Data Visualization**: ApexCharts for analytics dashboards
- **Form Handling**: React Hook Form

### Database Design

#### Core Entities

- TODO

#### Time-Series Data

- TODO

### Workflow Automation

- Scheduled data collection from GitHub API
- Integration with the backend API for data processing
- Report generation and distribution
- Alert notifications based on metric thresholds
- Data transformation and enrichment
- Database operations via the backend API

## Development Workflow

1. Make changes to the application codebase
2. Run tests to verify functionality
3. Submit pull requests for review
4. Deploy to production after approval

## Deployment Architecture

The application can be deployed in multiple ways:

- **Development**: Single-node deployment with docker-compose.yml
- **Firebase App Hosting**: Deployment to Firebase App Hosting for production (see [Firebase Deployment Guide](./firebase-deployment.md))
- **Custom Hosting**: Scalable deployment with proper resource allocation with terraform (TODO)
