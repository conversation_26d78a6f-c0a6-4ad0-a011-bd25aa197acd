# Pulse Design System - Midnight + Electric-Blue Theme

## Overview

The Pulse SaaS dashboard uses a modern "Midnight + Electric-Blue" color palette inspired by top developer tools like GitHub Dark, Vercel, and Linear. This design system ensures consistency, accessibility, and a premium user experience.

## Core Colors

### Primary Colors (Hex Values)

| Color | Hex | Usage |
|-------|-----|-------|
| `--bg-primary` | `#0D1117` | Nearly-black midnight background |
| `--bg-surface` | `#161B22` | Elevated cards & sidebars |
| `--border-subtle` | `#30363D` | Subtle borders and dividers |
| `--text-primary` | `#F8FAFC` | Primary text (98% white) |
| `--text-muted` | `#94A3B8` | Secondary/muted text |

### Accent Colors

| Color | Hex | Usage |
|-------|-----|-------|
| `--accent-primary` | `#3B82F6` | Electric blue primary accent |
| `--accent-hover` | `#2563EB` | Hover state for primary accent |
| `--accent-subtle` | `#1E40AF` | Subtle accent variations |

### Semantic Colors

| Color | Hex | Usage |
|-------|-----|-------|
| `--success` | `#22C55E` | Success states, positive actions |
| `--warning` | `#FACC15` | Warning states, caution |
| `--danger` | `#EF4444` | Error states, destructive actions |
| `--info` | `#0EA5E9` | Informational states |

## Design System Rules

### 1. Contrast Requirements
- **Minimum contrast ratio**: 4.5:1 for all text on surfaces
- Primary text (`#F8FAFC`) on primary background (`#0D1117`) = 15.8:1 ✅
- Muted text (`#94A3B8`) on primary background (`#0D1117`) = 5.9:1 ✅

### 2. Spacing Grid (8pt System)
```css
--spacing-xs: 0.25rem;   /* 4px */
--spacing-sm: 0.5rem;    /* 8px */
--spacing-md: 1rem;      /* 16px */
--spacing-lg: 1.5rem;    /* 24px */
--spacing-xl: 2rem;      /* 32px */
--spacing-2xl: 3rem;     /* 48px */
```

### 3. Border Radius
- **Cards**: `12px` for modern, friendly appearance
- **Buttons**: `12px` for consistency
- **Small elements**: `8px` for subtle rounding

### 4. Shadows
- **Cards**: `0 2px 6px rgba(0, 0, 0, 0.4)`
- **Card hover**: `0 4px 12px rgba(0, 0, 0, 0.5)`
- **Modals**: `0 8px 32px rgba(0, 0, 0, 0.6)`

### 5. Hover/Active States
- Accent colors brighten/darken by 8% for interactive states
- Use `--accent-hover` for primary button hover states
- Maintain accessibility contrast ratios in all states

## Usage Guidelines

### When to Use Surface vs Primary Background

**Primary Background (`--bg-primary`)**:
- Main application background
- Full-screen layouts
- Behind content areas

**Surface Background (`--bg-surface`)**:
- Cards and panels
- Sidebars and navigation
- Modal backgrounds
- Elevated content areas

### Accent Color Hierarchy

1. **Primary Accent (`--accent-primary`)**: Main CTAs, primary buttons, active states
2. **Hover Accent (`--accent-hover`)**: Interactive hover states
3. **Subtle Accent (`--accent-subtle`)**: Secondary actions, less prominent elements

### Text Color Usage

1. **Primary Text (`--text-primary`)**: Headings, primary content, important labels
2. **Muted Text (`--text-muted`)**: Secondary content, descriptions, metadata
3. **Disabled Text**: Use `--text-muted` with 50% opacity

## Implementation Examples

### CSS Custom Properties
```css
.card {
  background-color: var(--bg-surface);
  border: 1px solid var(--border-subtle);
  border-radius: var(--radius-card);
  box-shadow: var(--shadow-card);
  color: var(--text-primary);
}

.button-primary {
  background-color: var(--accent-primary);
  border-radius: var(--radius-button);
  color: var(--text-primary);
}

.button-primary:hover {
  background-color: var(--accent-hover);
}
```

### Tailwind CSS Classes
```html
<!-- Card component -->
<div class="bg-bg-surface border border-border-subtle rounded-card shadow-card text-text-primary">
  <h2 class="text-text-primary">Card Title</h2>
  <p class="text-text-muted">Card description</p>
</div>

<!-- Primary button -->
<button class="bg-accent-primary hover:bg-accent-hover text-text-primary rounded-button px-6 py-3">
  Primary Action
</button>
```

### Material-UI Theme Usage
```tsx
import { useTheme } from '@mui/material/styles';

const MyComponent = () => {
  const theme = useTheme();
  
  return (
    <Card sx={{
      backgroundColor: theme.palette.background.paper, // Uses --bg-surface
      color: theme.palette.text.primary, // Uses --text-primary
    }}>
      <Button variant="contained" color="primary">
        {/* Uses --accent-primary */}
        Primary Action
      </Button>
    </Card>
  );
};
```

## Chart Colors

For data visualization, use the following color sequence:
1. `--accent-primary` (#3B82F6)
2. `--success` (#22C55E)
3. `--warning` (#FACC15)
4. `--info` (#0EA5E9)
5. `--danger` (#EF4444)

## Accessibility Notes

- All color combinations meet WCAG AA standards
- Focus indicators use `--accent-primary` with 2px outline
- Interactive elements have minimum 44px touch targets
- Color is never the only way to convey information

## Color Token Reference Table

| Token Name | CSS Variable | Hex Value | RGB | Usage Context |
|------------|--------------|-----------|-----|---------------|
| **Backgrounds** |
| Primary | `--bg-primary` | `#0D1117` | `13, 17, 23` | Main app background |
| Surface | `--bg-surface` | `#161B22` | `22, 27, 34` | Cards, sidebars |
| **Borders** |
| Subtle | `--border-subtle` | `#30363D` | `48, 54, 61` | Dividers, borders |
| **Text** |
| Primary | `--text-primary` | `#F8FAFC` | `248, 250, 252` | Headings, primary content |
| Muted | `--text-muted` | `#94A3B8` | `148, 163, 184` | Secondary content |
| **Accents** |
| Primary | `--accent-primary` | `#3B82F6` | `59, 130, 246` | CTAs, primary actions |
| Hover | `--accent-hover` | `#2563EB` | `37, 99, 235` | Hover states |
| Subtle | `--accent-subtle` | `#1E40AF` | `30, 64, 175` | Secondary accents |
| **Semantic** |
| Success | `--success` | `#22C55E` | `34, 197, 94` | Success states |
| Warning | `--warning` | `#FACC15` | `250, 204, 21` | Warning states |
| Danger | `--danger` | `#EF4444` | `239, 68, 68` | Error states |
| Info | `--info` | `#0EA5E9` | `14, 165, 233` | Info states |

## Updated Tailwind Config Snippet

```typescript
// tailwind.config.ts
import type { Config } from 'tailwindcss';

const config: Config = {
  content: ['./src/**/*.{js,ts,jsx,tsx,mdx}'],
  darkMode: 'class',
  theme: {
    extend: {
      colors: {
        // Core Midnight + Electric-Blue palette
        'bg-primary': '#0D1117',
        'bg-surface': '#161B22',
        'border-subtle': '#30363D',
        'text-primary': '#F8FAFC',
        'text-muted': '#94A3B8',
        'accent-primary': '#3B82F6',
        'accent-hover': '#2563EB',
        'accent-subtle': '#1E40AF',
        success: '#22C55E',
        warning: '#FACC15',
        danger: '#EF4444',
        info: '#0EA5E9',
      },
      borderRadius: {
        'card': '12px',
        'button': '12px',
      },
      boxShadow: {
        'card': '0 2px 6px rgba(0, 0, 0, 0.4)',
        'card-hover': '0 4px 12px rgba(0, 0, 0, 0.5)',
        'modal': '0 8px 32px rgba(0, 0, 0, 0.6)',
      },
    },
  },
  plugins: [],
};

export default config;
```

## Browser Support

This color system supports:
- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

CSS custom properties are used for maximum compatibility and easy theming.
