import 'server-only';

import { CompanySize } from '@prisma/client';
import { NextResponse } from 'next/server';

import { db } from '@/services/db';

/**
 * @swagger
 * /api/leads:
 *   post:
 *     tags:
 *       - Leads
 *     summary: Create a new lead
 *     description: Submit lead capture form data for potential customers
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - fullName
 *               - email
 *               - companyName
 *               - companySize
 *             properties:
 *               fullName:
 *                 type: string
 *                 description: Full name of the lead
 *                 minLength: 1
 *                 maxLength: 100
 *               email:
 *                 type: string
 *                 format: email
 *                 description: Email address of the lead
 *               companyName:
 *                 type: string
 *                 description: Name of the company
 *                 minLength: 1
 *                 maxLength: 100
 *               jobTitle:
 *                 type: string
 *                 description: Job title of the lead (optional)
 *                 maxLength: 100
 *               companySize:
 *                 type: string
 *                 enum: [STARTUP_1_10, SMALL_11_50, MEDIUM_51_200, LARGE_201_1000, ENTERPRISE_1000_PLUS]
 *                 description: Size of the company
 *               hearAboutUs:
 *                 type: string
 *                 description: How the lead heard about us (optional)
 *                 maxLength: 200
 *     responses:
 *       201:
 *         description: Lead created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 id:
 *                   type: string
 *                   description: Unique identifier for the lead
 *                 message:
 *                   type: string
 *                   description: Success message
 *       400:
 *         description: Invalid input data
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message
 *       409:
 *         description: Email already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message
 *       500:
 *         description: Server error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 error:
 *                   type: string
 *                   description: Error message
 */
export async function POST(request: Request) {
  try {
    // Parse the request body
    const body = await request.json();

    // Validate required fields
    if (!body.fullName || typeof body.fullName !== 'string' || body.fullName.trim() === '') {
      return NextResponse.json({ error: 'Full name is required' }, { status: 400 });
    }

    if (body.fullName.length > 100) {
      return NextResponse.json({ error: 'Full name must be 100 characters or less' }, { status: 400 });
    }

    if (!body.email || typeof body.email !== 'string' || body.email.trim() === '') {
      return NextResponse.json({ error: 'Email is required' }, { status: 400 });
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(body.email)) {
      return NextResponse.json({ error: 'Invalid email format' }, { status: 400 });
    }

    if (!body.companyName || typeof body.companyName !== 'string' || body.companyName.trim() === '') {
      return NextResponse.json({ error: 'Company name is required' }, { status: 400 });
    }

    if (body.companyName.length > 100) {
      return NextResponse.json({ error: 'Company name must be 100 characters or less' }, { status: 400 });
    }

    if (!body.companySize || !Object.values(CompanySize).includes(body.companySize)) {
      return NextResponse.json({ error: 'Valid company size is required' }, { status: 400 });
    }

    // Validate optional fields
    if (body.jobTitle && (typeof body.jobTitle !== 'string' || body.jobTitle.length > 100)) {
      return NextResponse.json({ error: 'Job title must be 100 characters or less' }, { status: 400 });
    }

    if (body.hearAboutUs && (typeof body.hearAboutUs !== 'string' || body.hearAboutUs.length > 200)) {
      return NextResponse.json({ error: 'Hear about us field must be 200 characters or less' }, { status: 400 });
    }

    // Check if email already exists
    const existingLead = await db.lead.findUnique({
      where: { email: body.email.toLowerCase().trim() },
    });

    if (existingLead) {
      return NextResponse.json({ error: 'Email already registered' }, { status: 409 });
    }

    // Create the lead
    const lead = await db.lead.create({
      data: {
        fullName: body.fullName.trim(),
        email: body.email.toLowerCase().trim(),
        companyName: body.companyName.trim(),
        jobTitle: body.jobTitle?.trim() || null,
        companySize: body.companySize,
        hearAboutUs: body.hearAboutUs?.trim() || null,
      },
    });

    return NextResponse.json(
      {
        id: lead.id,
        message: 'Lead created successfully',
      },
      { status: 201 }
    );
  } catch (error) {
    console.error('Error creating lead:', error);
    return NextResponse.json({ error: 'Failed to create lead' }, { status: 500 });
  }
}
