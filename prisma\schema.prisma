generator client {
  provider = "prisma-client-js"
  previewFeatures = ["views"]
}

// https://www.npmjs.com/package/prisma-openapi
generator openapi {
  provider = "prisma-openapi"
  output   = "./openapi"
  generateJsDoc = true
  generateYaml  = false
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
  directUrl = env("DATABASE_DIRECT_URL")
}

model Lead {
  id          String @id @default(uuid())
  fullName    String
  email       String @unique
  companyName String
  jobTitle    String?
  companySize CompanySize
  hearAboutUs String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("leads")
}

enum CompanySize {
  STARTUP_1_10
  SMALL_11_50
  MEDIUM_51_200
  LARGE_201_1000
  ENTERPRISE_1000_PLUS

  @@map("company_size")
}
