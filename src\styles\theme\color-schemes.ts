import type { ColorSystemOptions } from '@mui/material/styles';

import { california, deepNavy, electricBlue, kepple, nevada, redOrange, shakespeare, stormGrey } from './colors';
import type { ColorScheme } from './types';

export const colorSchemes = {
  dark: {
    palette: {
      action: {
        disabledBackground: 'rgba(0, 0, 0, 0.12)',
        hover: 'rgba(255, 255, 255, 0.08)', // Add hover action for dark theme
      },
      background: {
        default: deepNavy[950], // #0D1117 - nearly-black midnight
        defaultChannel: '13 17 23',
        paper: deepNavy[900], // #161B22 - elevated cards & sidebars
        paperChannel: '22 27 34',
        level1: deepNavy[800], // #1e293b
        level2: deepNavy[700], // #334155
        level3: deepNavy[600], // #475569
      },
      common: { black: '#000000', white: '#ffffff' },
      divider: '#30363D', // --border-subtle
      dividerChannel: '48 54 61',
      error: {
        ...redOrange,
        light: redOrange[300],
        main: redOrange[400],
        dark: redOrange[500],
        contrastText: 'var(--mui-palette-common-white)',
      },
      info: {
        ...electricBlue,
        light: electricBlue[300],
        main: electricBlue[400],
        dark: electricBlue[500],
        contrastText: 'var(--mui-palette-common-white)',
      },
      neutral: { ...nevada },
      primary: {
        ...electricBlue,
        light: electricBlue[300],
        main: electricBlue[500],
        dark: electricBlue[700],
        contrastText: 'var(--mui-palette-common-white)',
      },
      secondary: {
        ...deepNavy,
        light: deepNavy[300],
        main: deepNavy[500],
        dark: deepNavy[700],
        contrastText: 'var(--mui-palette-common-white)',
      },
      success: {
        ...kepple,
        light: kepple[300],
        main: kepple[400],
        dark: kepple[500],
        contrastText: 'var(--mui-palette-common-black)',
      },
      text: {
        primary: '#F8FAFC', // --text-primary (98% white)
        primaryChannel: '248 250 252',
        secondary: '#94A3B8', // --text-muted
        secondaryChannel: '148 163 184',
        disabled: nevada[600],
      },
      warning: {
        ...california,
        light: california[300],
        main: california[400],
        dark: california[500],
        contrastText: 'var(--mui-palette-common-black)',
      },
    },
  },
  light: {
    palette: {
      action: {
        disabledBackground: 'rgba(0, 0, 0, 0.06)',
        hover: 'rgba(0, 0, 0, 0.04)', // Add hover action for light theme
      },
      background: {
        default: 'var(--mui-palette-common-white)',
        defaultChannel: '255 255 255',
        paper: 'var(--mui-palette-neutral-50)',
        paperChannel: '249 250 251',
        level1: 'var(--mui-palette-neutral-100)',
        level2: 'var(--mui-palette-neutral-200)',
        level3: 'var(--mui-palette-neutral-300)',
      },
      common: { black: '#000000', white: '#ffffff' },
      divider: 'var(--mui-palette-neutral-200)',
      dividerChannel: '220 223 228',
      error: {
        ...redOrange,
        light: redOrange[400],
        main: redOrange[500],
        dark: redOrange[600],
        contrastText: 'var(--mui-palette-common-white)',
      },
      info: {
        ...shakespeare,
        light: shakespeare[400],
        main: shakespeare[500],
        dark: shakespeare[600],
        contrastText: 'var(--mui-palette-common-white)',
      },
      neutral: { ...stormGrey },
      primary: {
        ...electricBlue,
        light: electricBlue[400],
        main: electricBlue[500],
        dark: electricBlue[700],
        contrastText: 'var(--mui-palette-common-white)',
      },
      secondary: {
        ...deepNavy,
        light: deepNavy[400],
        main: deepNavy[600],
        dark: deepNavy[800],
        contrastText: 'var(--mui-palette-common-white)',
      },
      success: {
        ...kepple,
        light: kepple[400],
        main: kepple[500],
        dark: kepple[600],
        contrastText: 'var(--mui-palette-common-white)',
      },
      text: {
        primary: 'var(--mui-palette-neutral-900)',
        primaryChannel: '33 38 54',
        secondary: 'var(--mui-palette-neutral-500)',
        secondaryChannel: '102 112 133',
        disabled: 'var(--mui-palette-neutral-400)',
      },
      warning: {
        ...california,
        light: california[400],
        main: california[500],
        dark: california[600],
        contrastText: 'var(--mui-palette-common-white)',
      },
    },
  },
} satisfies Partial<Record<ColorScheme, ColorSystemOptions>>;
